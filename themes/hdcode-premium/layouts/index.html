{{ define "main" }}

<!-- Hero Section -->
<section id="hero" class="hero-section">
    <div class="hero-background">
        <div class="hero-grid"></div>
        <div class="hero-particles"></div>
    </div>

    <div class="hero-container">
        <div class="hero-content">
            <div class="hero-badge">
                <span>{{ .Site.Params.hero.badge }}</span>
            </div>

            <h1 class="hero-title">
                <span class="title-line">{{ .Site.Params.hero.title_line1 }}</span>
                <span class="title-line">with <span class="hdcode-animated" id="hdcode-text">HDCode</span></span>
            </h1>

            <p class="hero-description">
                {{ .Site.Params.hero.description }}
            </p>

            <div class="hero-actions">
                <a href="#contact" class="primary-button">
                    <span>{{ .Site.Params.hero.primary_button_text }}</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
                {{ if .Site.Params.hero.show_secondary_button }}
                <a href="#portfolio" class="secondary-button">
                    <i class="fas fa-play"></i>
                    <span>{{ .Site.Params.hero.secondary_button_text }}</span>
                </a>
                {{ end }}
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section id="services" class="services-section">
    <div class="section-container">
        <div class="section-header">
            <div class="section-badge">{{ .Site.Params.services.badge }}</div>
            <h2 class="section-title">{{ .Site.Params.services.title }}</h2>
            <p class="section-description">
                {{ .Site.Params.services.description }}
            </p>
        </div>

        <div class="services-grid">
            {{ range .Site.Params.services.items }}
            <div class="service-card{{ if .featured }} featured{{ end }}">
                {{ if .featured }}
                <div class="service-badge">{{ .badge }}</div>
                {{ end }}
                <div class="service-icon">
                    <i class="{{ .icon }}"></i>
                </div>
                <h3 class="service-title">{{ .title }}</h3>
                <p class="service-description">
                    {{ .description }}
                </p>
                <ul class="service-features">
                    {{ range .features }}
                    <li>{{ . }}</li>
                    {{ end }}
                </ul>
                <a href="#contact" class="service-link">
                    <span>{{ .Site.Params.services.learn_more_text }}</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            {{ end }}
        </div>
    </div>
</section>

<!-- Portfolio Section -->
{{ if .Site.Params.portfolio.enabled }}
<section id="portfolio" class="portfolio-section">
    <div class="section-container">
        <div class="section-header">
            <div class="section-badge">{{ .Site.Params.portfolio.badge }}</div>
            <h2 class="section-title">{{ .Site.Params.portfolio.title }}</h2>
            <p class="section-description">
                {{ .Site.Params.portfolio.description }}
            </p>
        </div>

        <div class="portfolio-grid">
            {{ range .Site.Params.portfolio.items }}
            <div class="portfolio-item">
                <div class="portfolio-image">
                    <div class="portfolio-preview">
                        <i class="{{ .icon }}"></i>
                        <div class="preview-text">
                            <h4>{{ .preview_title }}</h4>
                            <span>{{ .preview_subtitle }}</span>
                        </div>
                    </div>
                    <div class="portfolio-overlay">
                        <div class="portfolio-info">
                            <h3>{{ .title }}</h3>
                            <p>{{ .description }}</p>
                            <div class="portfolio-tech">
                                {{ range .technologies }}
                                <span>{{ . }}</span>
                                {{ end }}
                            </div>
                        </div>
                        <div class="portfolio-actions">
                            {{ if .demo_link }}
                            <a href="{{ .demo_link }}" class="portfolio-link">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                            {{ end }}
                            {{ if .github_link }}
                            <a href="{{ .github_link }}" class="portfolio-link">
                                <i class="fab fa-github"></i>
                            </a>
                            {{ end }}
                            {{ if .app_store_link }}
                            <a href="{{ .app_store_link }}" class="portfolio-link">
                                <i class="fab fa-app-store"></i>
                            </a>
                            {{ end }}
                            {{ if .play_store_link }}
                            <a href="{{ .play_store_link }}" class="portfolio-link">
                                <i class="fab fa-google-play"></i>
                            </a>
                            {{ end }}
                        </div>
                    </div>
                </div>
            </div>
            {{ end }}
        </div>

        <div class="portfolio-cta">
            <a href="#contact" class="primary-button">
                <span>{{ .Site.Params.portfolio.cta_text }}</span>
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
    </div>
</section>
{{ end }}

<!-- Experience Domains Section -->
<section id="experience" class="experience-section">
    <div class="experience-background">
        <div class="experience-particles"></div>
        <div class="experience-orbs"></div>
    </div>

    <div class="section-container">
        <div class="section-header">
            <div class="section-badge">{{ .Site.Params.experience.badge }}</div>
            <h2 class="section-title">{{ .Site.Params.experience.title }}</h2>
            <p class="section-description">
                {{ .Site.Params.experience.description }}
            </p>
        </div>

        <div class="experience-grid">
            {{ range $index, $domain := .Site.Params.experience.domains }}
            <div class="experience-card" data-index="{{ $index }}">
                <div class="experience-card-background"></div>
                <div class="experience-card-content">
                    <div class="experience-icon-container">
                        <div class="experience-icon-bg" style="background: linear-gradient(135deg, {{ $domain.color }}20, {{ $domain.color }}10);"></div>
                        <div class="experience-icon" style="color: {{ $domain.color }};">
                            <i class="{{ $domain.icon }}"></i>
                        </div>
                    </div>
                    <h3 class="experience-name">{{ $domain.name }}</h3>
                    <div class="experience-glow" style="background: radial-gradient(circle, {{ $domain.color }}15 0%, transparent 70%);"></div>
                </div>
            </div>
            {{ end }}
        </div>
    </div>
</section>

<!-- About Section -->
<section id="about" class="about-section">
    <div class="section-container">
        <div class="about-content">
            <div class="about-text">
                <div class="section-badge">{{ .Site.Params.about.badge }}</div>
                <h2 class="section-title">{{ .Site.Params.about.title }}</h2>
                <p class="about-description">
                    {{ .Site.Params.about.description }}
                </p>

                <div class="about-stats">
                    {{ range .Site.Params.about.stats }}
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="{{ .icon }}"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ index $.Site.Params.global_stats .number_key }}</div>
                            <div class="stat-label">{{ .label }}</div>
                        </div>
                    </div>
                    {{ end }}
                </div>


            </div>

            <div class="about-visual">
                <div class="tech-stack">
                    {{ range .Site.Params.about.technologies }}
                    <div class="tech-item">
                        <i class="{{ .icon }}"></i>
                        <span>{{ .name }}</span>
                    </div>
                    {{ end }}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section id="contact" class="contact-section">
    <div class="section-container">
        <div class="section-header">
            <div class="section-badge">{{ .Site.Params.contact.badge }}</div>
            <h2 class="section-title">{{ .Site.Params.contact.title }}</h2>
            <p class="section-description">
                {{ .Site.Params.contact.description }}
            </p>
        </div>

        <div class="contact-content">
            <div class="contact-info">
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="contact-details">
                        <h4>{{ .Site.Params.contact.email_label }}</h4>
                        <p>{{ .Site.Params.email }}</p>
                    </div>
                </div>

                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="contact-details">
                        <h4>{{ .Site.Params.contact.availability_label }}</h4>
                        <p>{{ .Site.Params.contact.availability }}</p>
                    </div>
                </div>
            </div>

            <div class="contact-action">
                <div class="contact-cta">
                    <h3>Ready to Start Your Project?</h3>
                    <p>Send us an email with your project details and we'll get back to you within 24 hours.</p>

                    <a href="mailto:{{ .Site.Params.email }}?subject=Project Inquiry - {{ .Site.Title }}&body=Hi HDCode Team,%0D%0A%0D%0AI'm interested in discussing a project with you.%0D%0A%0D%0AProject Type: %0D%0ATimeline: %0D%0ABudget Range: %0D%0A%0D%0AProject Details:%0D%0A%0D%0A%0D%0ABest regards,"
                       class="primary-button email-button">
                        <span>Start Your Project</span>
                        <i class="fas fa-envelope"></i>
                    </a>

                    <div class="contact-benefits">
                        <div class="benefit-item">
                            <i class="fas fa-check-circle"></i>
                            <span>Free consultation</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-check-circle"></i>
                            <span>24-hour response</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-check-circle"></i>
                            <span>No commitment required</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{{ end }}
