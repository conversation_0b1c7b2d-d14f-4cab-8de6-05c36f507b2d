<!DOCTYPE html>
<html lang="{{ .Site.LanguageCode | default "en" }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{{ if .IsHome }}{{ .Site.Params.description }}{{ else }}{{ .Description | default .Site.Params.description }}{{ end }}">
    <meta name="keywords" content="{{ .Site.Params.keywords }}">
    <meta name="author" content="{{ .Site.Params.author }}">
    
    <!-- Open Graph -->
    <meta property="og:title" content="{{ if .IsHome }}{{ .Site.Title }}{{ else }}{{ .Title }} | {{ .Site.Title }}{{ end }}">
    <meta property="og:description" content="{{ if .IsHome }}{{ .Site.Params.description }}{{ else }}{{ .Description | default .Site.Params.description }}{{ end }}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ .Permalink }}">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:title" content="{{ if .IsHome }}{{ .Site.Title }}{{ else }}{{ .Title }} | {{ .Site.Title }}{{ end }}">
    <meta property="twitter:description" content="{{ if .IsHome }}{{ .Site.Params.description }}{{ else }}{{ .Description | default .Site.Params.description }}{{ end }}">

    <title>{{ if .IsHome }}{{ .Site.Title }} - {{ .Site.Params.description }}{{ else }}{{ .Title }} | {{ .Site.Title }}{{ end }}</title>

    <!-- Premium Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Main stylesheet -->
    <link rel="stylesheet" href="{{ "css/main.css" | relURL }}">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ "favicon.ico" | relURL }}">

    <!-- Structured Data -->
    {{ if .IsHome }}
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "{{ .Site.Title }}",
        "description": "{{ .Site.Params.description }}",
        "url": "{{ .Site.BaseURL }}",
        "email": "{{ .Site.Params.email }}",
        "telephone": "{{ .Site.Params.phone }}"
    }
    </script>
    {{ end }}
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">{{ .Site.Title }}</div>
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- Navigation -->
    {{ partial "header.html" . }}

    <!-- Main Content -->
    <main>
        {{ block "main" . }}{{ end }}
    </main>

    <!-- Footer -->
    {{ partial "footer.html" . }}

    <!-- Scripts -->
    <script src="{{ "js/premium.js" | relURL }}"></script>
</body>
</html>
