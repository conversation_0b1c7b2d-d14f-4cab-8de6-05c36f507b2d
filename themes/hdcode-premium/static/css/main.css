/**
 * HD Digital Premium Theme - Main CSS
 * 
 * This is the main CSS file that imports all component and section styles
 * in the correct order to ensure proper cascade and specificity.
 * 
 * Import Order:
 * 1. Base styles (variables, reset, typography)
 * 2. Components (reusable UI elements)
 * 3. Sections (page-specific layouts)
 * 4. Utilities (animations, responsive helpers)
 * 
 * @version 2.0.0
 * <AUTHOR>
 */

/* ===== BASE STYLES ===== */
/* These must be imported first to establish the foundation */

/* CSS Custom Properties - Color palette, spacing, typography */
@import url('./base/variables.css');

/* CSS Reset and base element styles */
@import url('./base/reset.css');

/* Typography styles and font utilities */
@import url('./base/typography.css');

/* ===== COMPONENT STYLES ===== */
/* Reusable UI components that can be used across sections */

/* Loading screen component */
@import url('./components/loading.css');

/* Header and navigation components */
@import url('./components/header.css');

/* Button components and variants */
@import url('./components/buttons.css');

/* Form components and input styles */
@import url('./components/forms.css');

/* Card components (service cards, stat cards, etc.) */
@import url('./components/cards.css');

/* ===== SECTION STYLES ===== */
/* Page-specific sections and layouts */

/* Hero section with background effects */
@import url('./sections/hero.css');

/* Services section and grid */
@import url('./sections/services.css');

/* Experience/domains section */
@import url('./sections/experience.css');

/* Portfolio section and project grid */
@import url('./sections/portfolio.css');

/* About section with stats and tech stack */
@import url('./sections/about.css');

/* Contact section and forms */
@import url('./sections/contact.css');

/* Footer section */
@import url('./sections/footer.css');

/* ===== UTILITY STYLES ===== */
/* Animations, responsive helpers, and utility classes */

/* Keyframes and animation utilities */
@import url('./utilities/animations.css');

/* Responsive utilities and media query helpers */
@import url('./utilities/responsive.css');

/* ===== THEME METADATA ===== */
/**
 * Theme Information:
 * - Name: HD Digital Premium Theme
 * - Version: 2.0.0 (Refactored)
 * - Architecture: Component-based CSS with utility-first approach
 * - Browser Support: Modern browsers (ES6+, CSS Grid, Flexbox)
 * - Responsive: Mobile-first design with 5 breakpoints
 * - Accessibility: WCAG 2.1 AA compliant
 * 
 * File Structure:
 * ├── base/           - Foundation styles (variables, reset, typography)
 * ├── components/     - Reusable UI components
 * ├── sections/       - Page-specific layouts
 * └── utilities/      - Animations and responsive helpers
 * 
 * Spacing Scale:
 * - xs: 8px   (0.5rem)
 * - sm: 12px  (0.75rem)
 * - md: 16px  (1rem)
 * - lg: 24px  (1.5rem)
 * - xl: 32px  (2rem)
 * - 2xl: 40px (2.5rem)
 * - 3xl: 48px (3rem)
 * - 4xl: 64px (4rem)
 * - 5xl: 80px (5rem)
 * 
 * Color Palette:
 * - Primary: Blue (#3b82f6) to Green (#10b981) gradient
 * - Secondary: Green (#10b981) to Amber (#f59e0b) gradient
 * - Accent: Amber (#f59e0b) to Pink (#ec4899) gradient
 * - Background: Dark slate tones (#0f172a, #1e293b, #334155)
 * 
 * Typography:
 * - Primary: Inter (sans-serif)
 * - Display: Playfair Display (serif)
 * - Mono: Monaco, Menlo, Ubuntu Mono (monospace)
 * 
 * Breakpoints:
 * - sm: 640px
 * - md: 768px
 * - lg: 1024px
 * - xl: 1280px
 * - 2xl: 1536px
 */
