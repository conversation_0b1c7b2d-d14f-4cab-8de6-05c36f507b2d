/**
 * Hero Section
 * HD Digital Premium Theme
 * 
 * Styles for the main hero section including background effects,
 * content layout, animations, and responsive design.
 */

/* ===== HERO SECTION STRUCTURE ===== */

.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding-top: 80px;
    background: linear-gradient(135deg, #000000 0%, #0a0a0a 25%, #111111 50%, #0a0a0a 75%, #000000 100%);
}

.hero-container {
    max-width: none;
    margin: 0;
    padding-left: max(var(--spacing-lg), calc((100vw - var(--container-max-width)) / 2 + var(--spacing-lg)));
    padding-right: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    position: relative;
    z-index: 2;
    min-height: calc(100vh - 120px);
    text-align: left;
}

.hero-content {
    z-index: 3;
    animation: heroContentFadeIn 1.2s ease-out;
    width: 100%;
}

/* ===== HERO BACKGROUND EFFECTS ===== */

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    background:
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(245, 158, 11, 0.08) 0%, transparent 70%);
}

.hero-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(59, 130, 246, 0.08) 1px, transparent 1px),
        linear-gradient(90deg, rgba(59, 130, 246, 0.08) 1px, transparent 1px);
    background-size: 60px 60px;
    animation: gridMove 30s linear infinite;
    opacity: 0.6;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 15% 15%, rgba(59, 130, 246, 0.12) 0%, transparent 40%),
        radial-gradient(circle at 85% 85%, rgba(16, 185, 129, 0.10) 0%, transparent 45%),
        radial-gradient(circle at 45% 65%, rgba(245, 158, 11, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(236, 72, 153, 0.06) 0%, transparent 35%);
    animation: particleFloat 20s ease-in-out infinite;
}

/* ===== HERO CONTENT ===== */

.hero-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-md);
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--radius-2xl);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-lg);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.hero-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.8s ease;
}

.hero-badge:hover::before {
    left: 100%;
}

.hero-title {
    font-family: var(--font-display);
    font-size: clamp(3rem, 6vw, 5rem);
    font-weight: var(--font-weight-black);
    line-height: 0.9;
    margin-bottom: var(--spacing-2xl);
    letter-spacing: -0.02em;
    position: relative;
}

.title-line {
    display: block;
    animation: titleLineSlideIn 0.8s ease-out forwards;
    opacity: 0;
    transform: translateX(-30px);
    margin-bottom: var(--spacing-xs);
}

.title-line:nth-child(1) { animation-delay: 0.2s; }
.title-line:nth-child(2) { animation-delay: 0.4s; }

.hero-description {
    font-size: var(--font-size-xl);
    color: var(--text-primary);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-xl);
    max-width: 580px;
    font-weight: var(--font-weight-normal);
    opacity: 0;
    animation: descriptionFadeIn 1s ease-out 0.8s forwards;
}

.hero-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    opacity: 0;
    animation: actionsFadeIn 1s ease-out 1s forwards;
}

/* ===== HDCODE ANIMATION ===== */

.hdcode-animated {
    font-weight: var(--font-weight-black);
    background: linear-gradient(135deg, #3b82f6 0%, #10b981 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: hdcodeGradientShift 4s ease-in-out infinite;
    transition: all var(--transition-normal);
}

.hdcode-animated:hover {
    background: linear-gradient(135deg, #60a5fa 0%, #34d399 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== HERO VISUAL ELEMENTS ===== */

.hero-visual {
    position: relative;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    animation: visualFadeIn 1.5s ease-out 0.5s forwards;
}

.hero-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    border-radius: var(--radius-3xl);
    overflow: hidden;
}

.hero-image-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(16, 185, 129, 0.15) 50%, rgba(245, 158, 11, 0.1) 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: heroOrb 8s ease-in-out infinite;
    filter: blur(40px);
}

.hero-image-container::after {
    content: '';
    position: absolute;
    top: 20%;
    right: 20%;
    width: 150px;
    height: 150px;
    background: linear-gradient(45deg, rgba(168, 85, 247, 0.15) 0%, rgba(6, 182, 212, 0.1) 100%);
    border-radius: 50%;
    animation: heroOrbSecondary 6s ease-in-out infinite reverse;
    filter: blur(30px);
}

/* Premium geometric shapes */
.hero-visual::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 80px;
    height: 80px;
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--radius-lg);
    animation: geometricFloat 4s ease-in-out infinite;
}

.hero-visual::after {
    content: '';
    position: absolute;
    bottom: 15%;
    right: 15%;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(245, 158, 11, 0.15) 100%);
    border-radius: 50%;
    animation: geometricPulse 3s ease-in-out infinite;
}

/* ===== ANIMATIONS ===== */

@keyframes heroContentFadeIn {
    0% { opacity: 0; transform: translateY(30px); }
    100% { opacity: 1; transform: translateY(0); }
}

@keyframes titleLineSlideIn {
    to { opacity: 1; transform: translateX(0); }
}

@keyframes descriptionFadeIn {
    to { opacity: 1; }
}

@keyframes actionsFadeIn {
    to { opacity: 1; }
}

@keyframes visualFadeIn {
    to { opacity: 1; }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(60px, 60px); }
}

@keyframes particleFloat {
    0%, 100% { opacity: 0.8; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

@keyframes hdcodeGradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes heroOrb {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
}

@keyframes heroOrbSecondary {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.6; }
    50% { transform: scale(1.3) rotate(180deg); opacity: 0.9; }
}

@keyframes geometricFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(45deg); }
}

@keyframes geometricPulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.2); opacity: 1; }
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1024px) {
    .hero-title {
        font-size: clamp(2.5rem, 8vw, 4rem);
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding-top: 60px;
        min-height: 85vh;
    }

    .hero-container {
        padding: 0 var(--spacing-lg);
        min-height: calc(85vh - 80px);
    }

    .hero-title {
        font-size: clamp(2rem, 10vw, 3.5rem);
        margin-bottom: var(--spacing-md);
    }

    .hero-description {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-lg);
    }

    .hero-actions {
        flex-direction: column;
        gap: var(--spacing-md);
        width: 100%;
        margin-bottom: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .hero-section {
        min-height: 80vh;
    }

    .hero-container {
        padding: 0 var(--spacing-md);
        min-height: calc(80vh - 60px);
    }

    .hero-badge {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
        margin-bottom: var(--spacing-sm);
    }

    .hero-title {
        font-size: clamp(1.8rem, 12vw, 3rem);
        line-height: 1.1;
        margin-bottom: var(--spacing-sm);
    }

    .hero-description {
        font-size: var(--font-size-base);
        line-height: var(--line-height-normal);
        margin-bottom: var(--spacing-md);
    }

    .hero-actions {
        margin-bottom: var(--spacing-md);
    }
}

/* ===== REDUCED MOTION ===== */

@media (prefers-reduced-motion: reduce) {
    .hdcode-animated {
        animation: none;
        background: linear-gradient(135deg, #3b82f6 0%, #10b981 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero-grid,
    .hero-particles,
    .hero-image-container::before,
    .hero-image-container::after,
    .hero-visual::before,
    .hero-visual::after {
        animation: none;
    }
}
