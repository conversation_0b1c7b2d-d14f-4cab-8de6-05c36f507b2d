/**
 * About Section
 * HD Digital Premium Theme
 * 
 * Styles for the about section including content layout,
 * statistics, tech stack, and responsive design.
 */

/* ===== ABOUT SECTION ===== */

.about-section {
    padding: var(--spacing-5xl) 0;
    background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
}

/* ===== ABOUT CONTENT ===== */

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-5xl);
    align-items: center;
}

.about-description {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-lg);
}

/* ===== ABOUT STATS ===== */

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

/* ===== TECH STACK ===== */

.tech-stack {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.tech-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
    text-align: center;
}

.tech-item:hover {
    transform: translateY(-5px);
    border-color: var(--accent-primary);
}

.tech-item i {
    font-size: var(--font-size-4xl);
    color: var(--accent-primary);
}

.tech-item span {
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1024px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }

    .about-stats {
        grid-template-columns: 1fr;
        max-width: 400px;
        margin: var(--spacing-lg) auto;
    }

    .tech-stack {
        grid-template-columns: repeat(2, 1fr);
        max-width: 400px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .about-section {
        padding: var(--spacing-4xl) 0;
    }

    .tech-stack {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .tech-item {
        padding: var(--spacing-sm);
    }

    .tech-item i {
        font-size: var(--font-size-3xl);
    }
}

@media (max-width: 480px) {
    .about-section {
        padding: var(--spacing-3xl) 0;
    }

    .about-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
        margin: var(--spacing-md) auto;
    }

    .tech-stack {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }

    .tech-item {
        padding: var(--spacing-sm);
    }

    .tech-item i {
        font-size: var(--font-size-2xl);
    }

    .tech-item span {
        font-size: var(--font-size-xs);
    }
}
