/**
 * Services Section
 * HD Digital Premium Theme
 * 
 * Styles for the services section including grid layout,
 * section headers, and service card arrangements.
 */

/* ===== SERVICES SECTION ===== */

.services-section {
    padding: var(--spacing-5xl) 0;
    background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

/* ===== SECTION STRUCTURE ===== */

.section-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-5xl);
}

.section-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-md);
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: var(--radius-2xl);
    color: var(--accent-secondary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-md);
    backdrop-filter: blur(10px);
}

.section-title {
    font-family: var(--font-display);
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-md);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-description {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    max-width: 600px;
    margin: 0 auto;
    line-height: var(--line-height-relaxed);
}

/* ===== SERVICES GRID ===== */

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1200px) {
    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }
}

@media (max-width: 768px) {
    .services-section {
        padding: var(--spacing-4xl) 0;
    }

    .section-header {
        margin-bottom: var(--spacing-4xl);
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
    }

    .section-container {
        padding: 0 var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .services-section {
        padding: var(--spacing-3xl) 0;
    }

    .section-header {
        margin-bottom: var(--spacing-3xl);
    }

    .services-grid {
        margin-top: var(--spacing-md);
        gap: var(--spacing-sm);
    }
}
