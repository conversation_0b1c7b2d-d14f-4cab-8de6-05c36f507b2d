/**
 * Experience Section
 * HD Digital Premium Theme
 * 
 * Styles for the experience/domains section including
 * background effects, grid layout, and card animations.
 */

/* ===== EXPERIENCE SECTION ===== */

.experience-section {
    position: relative;
    padding: var(--spacing-5xl) 0;
    background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    overflow: hidden;
}

/* ===== BACKGROUND EFFECTS ===== */

.experience-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}

.experience-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(245, 158, 11, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(236, 72, 153, 0.04) 0%, transparent 50%);
    animation: experienceParticleFloat 25s ease-in-out infinite;
}

.experience-orbs {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.experience-orbs::before {
    content: '';
    position: absolute;
    top: 15%;
    left: 10%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: experienceOrbFloat 20s ease-in-out infinite;
    filter: blur(40px);
}

.experience-orbs::after {
    content: '';
    position: absolute;
    bottom: 20%;
    right: 15%;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.08) 0%, transparent 70%);
    border-radius: 50%;
    animation: experienceOrbFloat 15s ease-in-out infinite reverse;
    filter: blur(30px);
}

/* ===== EXPERIENCE GRID ===== */

.experience-grid {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

/* ===== EXPERIENCE CARD ANIMATIONS ===== */

.experience-card[data-index="0"] { animation-delay: 0.1s; }
.experience-card[data-index="1"] { animation-delay: 0.2s; }
.experience-card[data-index="2"] { animation-delay: 0.3s; }
.experience-card[data-index="3"] { animation-delay: 0.4s; }
.experience-card[data-index="4"] { animation-delay: 0.5s; }
.experience-card[data-index="5"] { animation-delay: 0.6s; }
.experience-card[data-index="6"] { animation-delay: 0.7s; }
.experience-card[data-index="7"] { animation-delay: 0.8s; }
.experience-card[data-index="8"] { animation-delay: 0.9s; }

.experience-icon-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    transition: all var(--transition-slow);
    animation: experienceIconPulse 4s ease-in-out infinite;
}

.experience-card:hover .experience-icon-bg {
    transform: scale(1.2);
    opacity: 1;
}

.experience-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 150px;
    height: 150px;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity var(--transition-slow);
    z-index: 1;
    filter: blur(20px);
}

.experience-card:hover .experience-glow {
    opacity: 1;
}

/* ===== ANIMATIONS ===== */

@keyframes experienceCardFadeIn {
    to { opacity: 1; transform: translateY(0); }
}

@keyframes experienceParticleFloat {
    0%, 100% { opacity: 0.6; transform: scale(1) rotate(0deg); }
    33% { opacity: 0.8; transform: scale(1.05) rotate(120deg); }
    66% { opacity: 0.7; transform: scale(0.95) rotate(240deg); }
}

@keyframes experienceOrbFloat {
    0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.6; }
    50% { transform: translate(30px, -20px) scale(1.2); opacity: 0.8; }
}

@keyframes experienceIconPulse {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1024px) {
    .experience-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .experience-section {
        padding: var(--spacing-4xl) 0;
    }

    .experience-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
    }

    .experience-card:hover {
        transform: translateY(-8px) scale(1.01);
    }
}

@media (max-width: 480px) {
    .experience-section {
        padding: var(--spacing-3xl) 0;
    }

    .experience-grid {
        grid-template-columns: 1fr;
        margin-top: var(--spacing-md);
        gap: var(--spacing-sm);
    }
}

/* ===== REDUCED MOTION ===== */

@media (prefers-reduced-motion: reduce) {
    .experience-particles,
    .experience-orbs::before,
    .experience-orbs::after,
    .experience-icon-bg {
        animation: none;
    }
}
