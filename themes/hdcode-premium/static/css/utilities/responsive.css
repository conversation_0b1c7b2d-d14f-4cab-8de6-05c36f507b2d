/**
 * Responsive Utilities
 * HD Digital Premium Theme
 * 
 * Global responsive utilities and media query helpers
 * for consistent responsive behavior across components.
 */

/* ===== RESPONSIVE DISPLAY UTILITIES ===== */

/* Hide on mobile */
@media (max-width: 767px) {
    .hidden-mobile {
        display: none !important;
    }
}

/* Hide on tablet */
@media (min-width: 768px) and (max-width: 1023px) {
    .hidden-tablet {
        display: none !important;
    }
}

/* Hide on desktop */
@media (min-width: 1024px) {
    .hidden-desktop {
        display: none !important;
    }
}

/* Show only on mobile */
@media (min-width: 768px) {
    .mobile-only {
        display: none !important;
    }
}

/* Show only on tablet */
@media (max-width: 767px), (min-width: 1024px) {
    .tablet-only {
        display: none !important;
    }
}

/* Show only on desktop */
@media (max-width: 1023px) {
    .desktop-only {
        display: none !important;
    }
}

/* ===== RESPONSIVE TEXT UTILITIES ===== */

/* Text alignment */
@media (max-width: 768px) {
    .text-center-mobile {
        text-align: center !important;
    }
    
    .text-left-mobile {
        text-align: left !important;
    }
    
    .text-right-mobile {
        text-align: right !important;
    }
}

/* Font sizes */
@media (max-width: 768px) {
    .text-sm-mobile {
        font-size: var(--font-size-sm) !important;
    }
    
    .text-base-mobile {
        font-size: var(--font-size-base) !important;
    }
    
    .text-lg-mobile {
        font-size: var(--font-size-lg) !important;
    }
}

/* ===== RESPONSIVE SPACING UTILITIES ===== */

/* Margins */
@media (max-width: 768px) {
    .m-0-mobile { margin: 0 !important; }
    .mt-xs-mobile { margin-top: var(--spacing-xs) !important; }
    .mt-sm-mobile { margin-top: var(--spacing-sm) !important; }
    .mt-md-mobile { margin-top: var(--spacing-md) !important; }
    .mb-xs-mobile { margin-bottom: var(--spacing-xs) !important; }
    .mb-sm-mobile { margin-bottom: var(--spacing-sm) !important; }
    .mb-md-mobile { margin-bottom: var(--spacing-md) !important; }
}

/* Padding */
@media (max-width: 768px) {
    .p-0-mobile { padding: 0 !important; }
    .p-xs-mobile { padding: var(--spacing-xs) !important; }
    .p-sm-mobile { padding: var(--spacing-sm) !important; }
    .p-md-mobile { padding: var(--spacing-md) !important; }
}

/* ===== RESPONSIVE LAYOUT UTILITIES ===== */

/* Flexbox direction */
@media (max-width: 768px) {
    .flex-col-mobile {
        flex-direction: column !important;
    }
    
    .flex-row-mobile {
        flex-direction: row !important;
    }
}

/* Grid columns */
@media (max-width: 768px) {
    .grid-cols-1-mobile {
        grid-template-columns: 1fr !important;
    }
    
    .grid-cols-2-mobile {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (max-width: 480px) {
    .grid-cols-1-small {
        grid-template-columns: 1fr !important;
    }
}

/* ===== RESPONSIVE WIDTH UTILITIES ===== */

@media (max-width: 768px) {
    .w-full-mobile {
        width: 100% !important;
    }
    
    .w-auto-mobile {
        width: auto !important;
    }
}

/* ===== GLOBAL RESPONSIVE ADJUSTMENTS ===== */

/* Container padding adjustments */
@media (max-width: 768px) {
    .container,
    .section-container,
    .nav-container,
    .footer-container {
        padding-left: var(--spacing-md);
        padding-right: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .container,
    .section-container,
    .nav-container,
    .footer-container {
        padding-left: var(--spacing-sm);
        padding-right: var(--spacing-sm);
    }
}

/* Section padding adjustments */
@media (max-width: 768px) {
    .hero-section,
    .services-section,
    .experience-section,
    .portfolio-section,
    .about-section,
    .contact-section {
        padding-top: var(--spacing-4xl);
        padding-bottom: var(--spacing-4xl);
    }
}

@media (max-width: 480px) {
    .hero-section,
    .services-section,
    .experience-section,
    .portfolio-section,
    .about-section,
    .contact-section {
        padding-top: var(--spacing-3xl);
        padding-bottom: var(--spacing-3xl);
    }
}

/* ===== RESPONSIVE TYPOGRAPHY SCALE ===== */

/* Responsive headings */
@media (max-width: 768px) {
    .hero-title,
    .section-title,
    h1, .h1 {
        font-size: clamp(2rem, 8vw, 3rem);
    }
    
    h2, .h2 {
        font-size: clamp(1.5rem, 6vw, 2.5rem);
    }
    
    h3, .h3 {
        font-size: clamp(1.25rem, 5vw, 2rem);
    }
}

@media (max-width: 480px) {
    .hero-title,
    .section-title,
    h1, .h1 {
        font-size: clamp(1.75rem, 10vw, 2.5rem);
    }
    
    h2, .h2 {
        font-size: clamp(1.25rem, 8vw, 2rem);
    }
    
    h3, .h3 {
        font-size: clamp(1.125rem, 6vw, 1.5rem);
    }
}

/* ===== RESPONSIVE BUTTON ADJUSTMENTS ===== */

@media (max-width: 768px) {
    .primary-button,
    .secondary-button,
    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
    }
    
    .hero-actions,
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
}

/* ===== RESPONSIVE CARD ADJUSTMENTS ===== */

@media (max-width: 768px) {
    .service-card,
    .experience-card,
    .portfolio-item,
    .stat-card,
    .contact-item {
        margin-bottom: var(--spacing-md);
    }
}

/* ===== RESPONSIVE GRID ADJUSTMENTS ===== */

@media (max-width: 1200px) {
    .services-grid,
    .portfolio-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
    
    .experience-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .services-grid,
    .portfolio-grid,
    .experience-grid {
        grid-template-columns: 1fr;
    }
    
    .about-stats,
    .tech-stack {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .experience-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== RESPONSIVE NAVIGATION ===== */

@media (max-width: 768px) {
    .desktop-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
}

/* ===== PRINT STYLES ===== */

@media print {
    .premium-header,
    .mobile-menu,
    .hero-background,
    .hero-grid,
    .hero-particles,
    .experience-particles,
    .experience-orbs {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .hero-section,
    .services-section,
    .experience-section,
    .portfolio-section,
    .about-section,
    .contact-section {
        padding: var(--spacing-lg) 0;
        background: white !important;
    }
}
