/**
 * CSS Custom Properties (Variables)
 * HD Digital Premium Theme
 * 
 * This file contains all CSS custom properties used throughout the theme.
 * Organized by category for better maintainability.
 */

:root {
    /* ===== COLOR PALETTE ===== */
    
    /* Primary Colors - Dark theme foundation */
    --primary-900: #0f172a;
    --primary-800: #1e293b;
    --primary-700: #334155;
    --primary-600: #475569;
    --primary-500: #64748b;

    /* Accent Colors - Brand colors for highlights and CTAs */
    --accent-primary: #3b82f6;    /* Bright blue */
    --accent-secondary: #10b981;  /* Emerald green */
    --accent-tertiary: #f59e0b;   /* Warm amber */
    --accent-quaternary: #ec4899; /* Pink */
    --accent-purple: #8b5cf6;     /* Purple */
    --accent-cyan: #06b6d4;       /* Cyan */
    --accent-orange: #f97316;     /* Orange */

    /* Gradient Colors - For premium visual effects */
    --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #10b981 100%);
    --gradient-secondary: linear-gradient(135deg, #10b981 0%, #f59e0b 100%);
    --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #ec4899 100%);
    --gradient-hero: linear-gradient(135deg, #3b82f6 0%, #10b981 50%, #f59e0b 100%);

    /* Text Colors - Semantic text color hierarchy */
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
    --text-muted: #cbd5e1;
    --text-dark: #1e293b;

    /* Background Colors - Surface colors for different contexts */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: rgba(248, 250, 252, 0.08);
    --bg-glass: rgba(248, 250, 252, 0.12);

    /* ===== SPACING SCALE ===== */
    /* Consistent spacing scale based on 4px grid system */
    --spacing-xs: 0.5rem;    /* 8px */
    --spacing-sm: 0.75rem;   /* 12px */
    --spacing-md: 1rem;      /* 16px */
    --spacing-lg: 1.5rem;    /* 24px */
    --spacing-xl: 2rem;      /* 32px */
    --spacing-2xl: 2.5rem;   /* 40px */
    --spacing-3xl: 3rem;     /* 48px */
    --spacing-4xl: 4rem;     /* 64px */
    --spacing-5xl: 5rem;     /* 80px */

    /* ===== LAYOUT ===== */
    --container-max-width: 1400px;
    --container-padding: var(--spacing-lg);

    /* ===== TYPOGRAPHY ===== */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-display: 'Playfair Display', serif;
    --font-mono: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;

    /* Font Sizes - Responsive typography scale */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */
    --font-size-5xl: 3rem;      /* 48px */

    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    /* ===== SHADOWS ===== */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-premium: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* ===== BORDER RADIUS ===== */
    --radius-xs: 0.125rem;   /* 2px */
    --radius-sm: 0.25rem;    /* 4px */
    --radius-md: 0.375rem;   /* 6px */
    --radius-lg: 0.5rem;     /* 8px */
    --radius-xl: 0.75rem;    /* 12px */
    --radius-2xl: 1rem;      /* 16px */
    --radius-3xl: 1.5rem;    /* 24px */
    --radius-full: 9999px;   /* Full rounded */

    /* Legacy border radius variables for backward compatibility */
    --border-radius-sm: var(--radius-md);
    --border-radius-md: var(--radius-lg);
    --border-radius-lg: var(--radius-xl);
    --border-radius-xl: var(--radius-2xl);
    --border-radius-2xl: var(--radius-3xl);

    /* ===== TRANSITIONS ===== */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;

    /* ===== Z-INDEX SCALE ===== */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;

    /* ===== BREAKPOINTS ===== */
    /* Note: These are for reference only, actual media queries use pixel values */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* ===== DARK MODE OVERRIDES ===== */
/* Additional variables for potential light mode support in the future */
@media (prefers-color-scheme: dark) {
    :root {
        /* Dark mode is the default, but this ensures consistency */
        --text-primary: #ffffff;
        --text-secondary: #e2e8f0;
        --text-muted: #cbd5e1;
        --bg-primary: #0f172a;
        --bg-secondary: #1e293b;
    }
}

/* ===== REDUCED MOTION ===== */
/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
    :root {
        --transition-fast: 0s;
        --transition-normal: 0s;
        --transition-slow: 0s;
    }
}
