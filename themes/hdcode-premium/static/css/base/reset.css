/**
 * Reset and Base Styles
 * HD Digital Premium Theme
 * 
 * This file contains CSS reset rules and base element styles.
 * Provides a consistent foundation across all browsers.
 */

/* ===== CSS RESET ===== */

/* Box sizing reset */
*,
*::before,
*::after {
    box-sizing: border-box;
}

/* Remove default margins and padding */
* {
    margin: 0;
    padding: 0;
}

/* ===== BASE ELEMENT STYLES ===== */

/* HTML and body setup */
html {
    scroll-behavior: smooth;
    font-size: 16px;
    line-height: 1.15; /* Normalize line-height */
    -webkit-text-size-adjust: 100%; /* Prevent adjustments of font size after orientation changes in iOS */
}

body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-normal);
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* ===== TYPOGRAPHY RESET ===== */

/* Headings */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    color: var(--text-primary);
}

/* Paragraphs */
p {
    line-height: var(--line-height-normal);
    color: var(--text-primary);
}

/* Links */
a {
    color: var(--accent-primary);
    text-decoration: none;
    transition: color var(--transition-normal);
}

a:hover {
    color: var(--accent-secondary);
}

/* Lists */
ul, ol {
    list-style: none;
}

/* Images and media */
img,
picture,
video,
canvas,
svg {
    display: block;
    max-width: 100%;
    height: auto;
}

/* Form elements */
input,
button,
textarea,
select {
    font: inherit;
    color: inherit;
}

/* Remove default button styles */
button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

/* Ensure buttons can have focus */
button:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* Input focus styles */
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* ===== ACCESSIBILITY ===== */

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus visible for keyboard navigation */
.focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ===== UTILITY CLASSES ===== */

/* Container */
.container {
    width: 100%;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

/* Text utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Display utilities */
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }

/* Flexbox utilities */
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }

/* Spacing utilities */
.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

/* Margin utilities */
.m-0 { margin: 0; }
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

/* Padding utilities */
.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

/* Width utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }

/* Height utilities */
.h-full { height: 100%; }
.h-auto { height: auto; }

/* Position utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Overflow utilities */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }
