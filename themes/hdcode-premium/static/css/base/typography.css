/**
 * Typography Styles
 * HD Digital Premium Theme
 * 
 * This file contains all typography-related styles including
 * headings, text utilities, and font-specific classes.
 */

/* ===== HEADING STYLES ===== */

h1, .h1 {
    font-family: var(--font-display);
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-extrabold);
    line-height: var(--line-height-tight);
    letter-spacing: -0.02em;
    margin-bottom: var(--spacing-lg);
}

h2, .h2 {
    font-family: var(--font-display);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: -0.01em;
    margin-bottom: var(--spacing-md);
}

h3, .h3 {
    font-family: var(--font-display);
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    margin-bottom: var(--spacing-md);
}

h4, .h4 {
    font-family: var(--font-primary);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-normal);
    margin-bottom: var(--spacing-sm);
}

h5, .h5 {
    font-family: var(--font-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-normal);
    margin-bottom: var(--spacing-sm);
}

h6, .h6 {
    font-family: var(--font-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    margin-bottom: var(--spacing-xs);
}

/* ===== PARAGRAPH AND TEXT STYLES ===== */

p, .text-body {
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-md);
}

.text-lead {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    font-weight: var(--font-weight-normal);
    color: var(--text-secondary);
}

.text-small {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
}

.text-xs {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
}

/* ===== FONT WEIGHT UTILITIES ===== */

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }
.font-black { font-weight: var(--font-weight-black); }

/* ===== FONT FAMILY UTILITIES ===== */

.font-primary { font-family: var(--font-primary); }
.font-display { font-family: var(--font-display); }
.font-mono { font-family: var(--font-mono); }

/* ===== TEXT COLOR UTILITIES ===== */

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-dark { color: var(--text-dark); }

.text-accent-primary { color: var(--accent-primary); }
.text-accent-secondary { color: var(--accent-secondary); }
.text-accent-tertiary { color: var(--accent-tertiary); }

/* ===== GRADIENT TEXT ===== */

.gradient-text {
    background: var(--gradient-primary);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
}

.gradient-text-hero {
    background: var(--gradient-hero);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
}

.gradient-text-secondary {
    background: var(--gradient-secondary);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== SPECIAL TEXT EFFECTS ===== */

.text-glow {
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* ===== CODE AND MONOSPACE ===== */

code {
    font-family: var(--font-mono);
    font-size: 0.875em;
    background: var(--bg-glass);
    padding: 0.125rem 0.375rem;
    border-radius: var(--radius-sm);
    color: var(--accent-primary);
}

pre {
    font-family: var(--font-mono);
    background: var(--bg-glass);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    overflow-x: auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

pre code {
    background: none;
    padding: 0;
    color: var(--text-primary);
}

/* ===== BLOCKQUOTES ===== */

blockquote {
    border-left: 4px solid var(--accent-primary);
    padding-left: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    font-style: italic;
    color: var(--text-secondary);
}

blockquote p {
    margin-bottom: var(--spacing-sm);
}

/* ===== LISTS ===== */

ul, ol {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-lg);
}

ul {
    list-style-type: disc;
}

ol {
    list-style-type: decimal;
}

li {
    margin-bottom: var(--spacing-xs);
    line-height: var(--line-height-relaxed);
}

/* Custom list styles */
.list-none {
    list-style: none;
    padding-left: 0;
}

.list-check li {
    position: relative;
    list-style: none;
    padding-left: var(--spacing-lg);
}

.list-check li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-secondary);
    font-weight: var(--font-weight-bold);
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */

@media (max-width: 768px) {
    h1, .h1 {
        font-size: var(--font-size-4xl);
    }
    
    h2, .h2 {
        font-size: var(--font-size-3xl);
    }
    
    h3, .h3 {
        font-size: var(--font-size-2xl);
    }
    
    .text-lead {
        font-size: var(--font-size-base);
    }
}

@media (max-width: 480px) {
    h1, .h1 {
        font-size: var(--font-size-3xl);
    }
    
    h2, .h2 {
        font-size: var(--font-size-2xl);
    }
    
    h3, .h3 {
        font-size: var(--font-size-xl);
    }
}
