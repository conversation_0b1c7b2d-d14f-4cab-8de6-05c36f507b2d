/**
 * Card Components
 * HD Digital Premium Theme
 * 
 * Reusable card components including service cards, stat cards,
 * portfolio items, and other card-based layouts.
 */

/* ===== BASE CARD STYLES ===== */

.card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-xl);
}

/* ===== SERVICE CARDS ===== */

.service-card {
    position: relative;
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-premium);
}

.service-card.featured {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    border-color: rgba(59, 130, 246, 0.3);
}

.service-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--gradient-accent);
    color: var(--text-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    border-radius: var(--radius-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.service-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-lg);
}

.service-icon i {
    font-size: var(--font-size-2xl);
    color: var(--text-primary);
}

.service-title {
    font-family: var(--font-display);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.service-description {
    color: var(--text-primary);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-md);
}

.service-features {
    list-style: none;
    margin-bottom: var(--spacing-md);
}

.service-features li {
    position: relative;
    padding-left: var(--spacing-lg);
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-primary);
    font-weight: var(--font-weight-bold);
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--accent-primary);
    text-decoration: none;
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    transition: all var(--transition-normal);
}

.service-link:hover {
    gap: var(--spacing-sm);
    color: var(--accent-secondary);
}

.service-link i {
    transition: transform var(--transition-normal);
}

.service-link:hover i {
    transform: translateX(3px);
}

/* ===== STAT CARDS ===== */

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 0.2);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-family: var(--font-display);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    line-height: 1;
}

/* ===== EXPERIENCE CARDS ===== */

.experience-card {
    position: relative;
    background: var(--bg-glass);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: var(--radius-2xl);
    padding: 0;
    transition: all var(--transition-slow);
    overflow: hidden;
    opacity: 0;
    transform: translateY(30px);
    animation: experienceCardFadeIn 0.8s ease-out forwards;
}

.experience-card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
    transition: all var(--transition-slow);
}

.experience-card:hover .experience-card-background {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
}

.experience-card-content {
    position: relative;
    z-index: 2;
    padding: var(--spacing-lg);
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 180px;
}

.experience-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-slow);
    z-index: 3;
}

.experience-card:hover::before {
    transform: scaleX(1);
}

.experience-card:hover {
    transform: translateY(-15px) scale(1.02);
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow:
        var(--shadow-premium),
        0 0 40px rgba(59, 130, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.experience-icon-container {
    position: relative;
    margin-bottom: var(--spacing-md);
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.experience-icon {
    position: relative;
    z-index: 2;
    font-size: var(--font-size-4xl);
    transition: all var(--transition-slow);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.experience-card:hover .experience-icon {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4));
}

.experience-name {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    line-height: var(--line-height-normal);
    margin: 0;
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    z-index: 2;
}

.experience-card:hover .experience-name {
    color: var(--text-primary);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* ===== CONTACT CARDS ===== */

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.contact-item:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 0.2);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-size: var(--font-size-2xl);
    flex-shrink: 0;
}

.contact-details h4 {
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.contact-details p {
    color: var(--text-primary);
    margin: 0;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .service-card,
    .card {
        padding: var(--spacing-md);
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm);
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
    }

    .experience-card-content {
        padding: var(--spacing-md);
        min-height: 160px;
    }

    .experience-icon-container {
        width: 60px;
        height: 60px;
        margin-bottom: var(--spacing-sm);
    }

    .experience-icon {
        font-size: var(--font-size-3xl);
    }

    .experience-name {
        font-size: var(--font-size-base);
        line-height: var(--line-height-tight);
    }
}

@media (max-width: 480px) {
    .service-card,
    .card {
        padding: var(--spacing-sm);
    }

    .service-icon {
        width: 50px;
        height: 50px;
        margin-bottom: var(--spacing-sm);
    }

    .service-title {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-xs);
    }

    .service-description {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-sm);
    }

    .contact-item {
        padding: var(--spacing-sm);
    }

    .contact-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-xl);
    }
}
