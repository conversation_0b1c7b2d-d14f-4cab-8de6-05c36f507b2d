/**
 * Loading Screen Component
 * HD Digital Premium Theme
 * 
 * Styles for the loading screen that appears while the page loads.
 * Includes animated spinner and logo effects.
 */

/* ===== LOADING SCREEN ===== */

#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    transition: opacity var(--transition-slow);
}

.loading-content {
    text-align: center;
}

.loading-logo {
    font-family: var(--font-display);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(59, 130, 246, 0.3);
    border-top: 3px solid var(--accent-primary);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

/* ===== ANIMATIONS ===== */

@keyframes spin {
    0% { 
        transform: rotate(0deg); 
    }
    100% { 
        transform: rotate(360deg); 
    }
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .loading-logo {
        font-size: var(--font-size-3xl);
    }
    
    .loading-spinner {
        width: 32px;
        height: 32px;
        border-width: 2px;
    }
}

@media (max-width: 480px) {
    .loading-logo {
        font-size: var(--font-size-2xl);
    }
    
    .loading-spinner {
        width: 28px;
        height: 28px;
    }
}
