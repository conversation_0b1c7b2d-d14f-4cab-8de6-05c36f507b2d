/**
 * Form Components
 * HD Digital Premium Theme
 * 
 * Comprehensive form styles including inputs, textareas,
 * form groups, validation states, and premium styling.
 */

/* ===== FORM CONTAINER ===== */

.premium-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    width: 100%;
}

.form-row {
    display: flex;
    gap: var(--spacing-md);
}

.form-row .form-group {
    flex: 1;
}

/* ===== FORM GROUPS ===== */

.form-group {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.form-label.required::after {
    content: '*';
    color: #ef4444;
    margin-left: var(--spacing-xs);
}

/* ===== INPUT STYLES ===== */

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: var(--text-secondary);
    opacity: 0.8;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: rgba(255, 255, 255, 0.12);
}

/* ===== TEXTAREA SPECIFIC ===== */

.form-textarea {
    resize: vertical;
    min-height: 120px;
    line-height: var(--line-height-relaxed);
}

/* ===== SELECT SPECIFIC ===== */

.form-select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23e2e8f0' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--spacing-md) center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: var(--spacing-3xl);
}

/* ===== INPUT VARIANTS ===== */

.form-input-sm {
    padding: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.form-input-lg {
    padding: var(--spacing-lg);
    font-size: var(--font-size-lg);
}

/* ===== VALIDATION STATES ===== */

.form-group.success .form-input,
.form-group.success .form-textarea,
.form-group.success .form-select {
    border-color: var(--accent-secondary);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-group.error .form-input,
.form-group.error .form-textarea,
.form-group.error .form-select {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group.warning .form-input,
.form-group.warning .form-textarea,
.form-group.warning .form-select {
    border-color: var(--accent-tertiary);
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

/* ===== HELP TEXT ===== */

.form-help {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

.form-error {
    font-size: var(--font-size-sm);
    color: #ef4444;
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.form-success {
    font-size: var(--font-size-sm);
    color: var(--accent-secondary);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* ===== CHECKBOX AND RADIO ===== */

.form-checkbox,
.form-radio {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
}

.form-checkbox input[type="checkbox"],
.form-radio input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: var(--accent-primary);
    cursor: pointer;
}

.form-checkbox label,
.form-radio label {
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

/* ===== FORM MESSAGES ===== */

.form-message {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-align: center;
    font-weight: var(--font-weight-semibold);
    transition: all var(--transition-normal);
}

.form-message.success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
}

.form-message.error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

.form-message.warning {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    color: var(--accent-tertiary);
}

.form-message.info {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    color: var(--accent-primary);
}

.form-message i {
    margin-right: var(--spacing-sm);
}

/* ===== FLOATING LABELS ===== */

.form-group.floating {
    position: relative;
}

.form-group.floating .form-input,
.form-group.floating .form-textarea {
    padding-top: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
}

.form-group.floating .form-label {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    transition: all var(--transition-normal);
    pointer-events: none;
    color: var(--text-muted);
}

.form-group.floating .form-input:focus + .form-label,
.form-group.floating .form-input:not(:placeholder-shown) + .form-label,
.form-group.floating .form-textarea:focus + .form-label,
.form-group.floating .form-textarea:not(:placeholder-shown) + .form-label {
    top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--accent-primary);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .premium-form {
        gap: var(--spacing-md);
    }

    .form-group {
        margin-bottom: var(--spacing-md);
    }

    .form-input,
    .form-textarea,
    .form-select {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .form-message {
        padding: var(--spacing-md);
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 480px) {
    .form-input,
    .form-textarea,
    .form-select {
        padding: var(--spacing-sm);
    }

    .form-textarea {
        min-height: 100px;
    }
}

/* ===== ACCESSIBILITY ===== */

.form-input:focus-visible,
.form-textarea:focus-visible,
.form-select:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* ===== DISABLED STATE ===== */

.form-input:disabled,
.form-textarea:disabled,
.form-select:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: rgba(255, 255, 255, 0.03);
}
