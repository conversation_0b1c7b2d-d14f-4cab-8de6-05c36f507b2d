/**
 * Header and Navigation Component
 * HD Digital Premium Theme
 * 
 * Styles for the main site header including navigation,
 * logo, mobile menu, and responsive behavior.
 */

/* ===== HEADER STRUCTURE ===== */

.premium-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.premium-nav {
    padding: var(--spacing-sm) 0;
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* ===== LOGO STYLES ===== */

.nav-logo .logo-link {
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo-text {
    font-family: var(--font-display);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    transition: all var(--transition-normal);
}

.logo-accent {
    color: var(--accent-primary);
    font-size: var(--font-size-3xl);
    margin-left: 2px;
}

.nav-logo:hover .logo-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== DESKTOP NAVIGATION ===== */

.desktop-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-link {
    position: relative;
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    transition: all var(--transition-normal);
    padding: var(--spacing-xs) 0;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
}

.nav-link:hover {
    color: var(--text-primary);
}

.nav-link:hover::before {
    width: 100%;
}

/* ===== MOBILE MENU ===== */

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
    padding: var(--spacing-xs);
}

.mobile-menu-toggle span {
    width: 25px;
    height: 2px;
    background: var(--text-primary);
    transition: all var(--transition-normal);
    border-radius: var(--radius-sm);
}

/* Mobile menu toggle animation */
.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
}

.mobile-menu-content {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.mobile-nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: color var(--transition-normal);
}

.mobile-nav-link:hover {
    color: var(--text-primary);
}

.mobile-nav-link:last-child {
    border-bottom: none;
}

.mobile-cta-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--gradient-primary);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--radius-2xl);
    font-weight: var(--font-weight-semibold);
    margin-top: var(--spacing-md);
    transition: all var(--transition-normal);
}

.mobile-cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .desktop-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mobile-menu.active {
        display: block;
        animation: slideDown 0.3s ease-out;
    }

    .nav-container {
        padding: 0 var(--spacing-md);
    }

    .logo-text {
        font-size: var(--font-size-xl);
    }

    .logo-accent {
        font-size: var(--font-size-2xl);
    }
}

@media (max-width: 480px) {
    .premium-nav {
        padding: var(--spacing-xs) 0;
    }

    .mobile-menu-content {
        padding: var(--spacing-md);
    }

    .logo-text {
        font-size: var(--font-size-lg);
    }

    .logo-accent {
        font-size: var(--font-size-xl);
    }
}

/* ===== ANIMATIONS ===== */

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== HEADER SCROLL EFFECTS ===== */

.premium-header.scrolled {
    background: rgba(0, 0, 0, 0.95);
    box-shadow: var(--shadow-lg);
}

.premium-header.scrolled .premium-nav {
    padding: var(--spacing-xs) 0;
}
